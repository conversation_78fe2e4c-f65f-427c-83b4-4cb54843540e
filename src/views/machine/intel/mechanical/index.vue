<template>
  <!-- 主机厂客户/客户情报/通机 -->
  <CommonTabs active="1">
    <template #searchArea>
      <SearchFormResource :params="originParams" @change="getParams" />
    </template>
    <el-row :gutter="20" style="margin-left: 0; margin-right: 0">
      <el-col :xs="24" :sm="24" :md="8">
        <chart
          v-loading="data.chartA.loading"
          :title="{ text: data.chartA.title }"
          :options="{
            yAxis: [{ name: '单位：(台)' }],
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipComponent, params, {
                  showTotal: true,
                  sortField: 'value'
                })
            },
            legend: { data: data.chartA.legendData }
          }"
          :series="data.chartA.data"
          height="270px"
          :precision="0"
          show-total
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <chart
          v-loading="data.chartB.loading"
          :title="{ text: data.chartB.title }"
          :options="{
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipSalesProportionYoYComponent, params, {
                  mapping: {
                    sales: 'sales',
                    proportion: 'prop',
                    yoy: 'propChange_sc'
                  },
                  showTotal: true,
                  sortField: 'value'
                })
            },
            // tooltip: { formatter: formatterChartB },
            yAxis: [{ name: '单位：(台)' }, { ...yAxisRight[0], name: '同比：(%)' }],
            legend: { data: data.chartB.legendData }
          }"
          :series="data.chartB.data"
          height="270px"
          :precision="0"
          show-total
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="8">
        <chart
          v-loading="data.chartC.loading"
          :title="{ text: data.chartC.title }"
          :options="{
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipComponent, params, {
                  showTotal: true,
                  sortField: 'value'
                })
            },
            yAxis: [{ name: '单位：(台)' }]
          }"
          :series="data.chartC.data"
          height="270px"
          :precision="0"
          show-total
        />
      </el-col>
      <el-col :xs="24" :sm="24" :md="16">
        <chart
          v-loading="data.chartD.loading"
          :title="{ text: data.chartD.title }"
          :options="{
            tooltip: {
              formatter: params =>
                TooltipFormatter(TooltipSalesProportionYoYComponent, params, {
                  mapping: {
                    sales: 'sales',
                    proportion: 'prop',
                    yoy: 'propChange_dl'
                  },
                  showTotal: true,
                  sortField: 'value'
                }),
              position: positionLeft
            },
            yAxis: [{ name: '单位：(台)' }, { ...yAxisRight[0], name: '占比：(%)' }]
          }"
          :series="data.chartD.data"
          height="270px"
          :precision="0"
          show-total
        />
      </el-col>
    </el-row>
  </CommonTabs>
</template>

<script setup lang="jsx">
import CommonTabs from '@/views/components/tabs/CommonTabs'
import chart from '@/views/components/echarts/chart.vue'
import SearchFormResource from './components/SearchFormResource.vue'
import { positionLeft } from '@/utils/echarts.js'
import calChartsData from '@/utils/hooks/calChartsData.js'
import { numberFormat } from '@/utils/format'
import {
  hostCustomerInstallMarketTrend,
  hostCustomerInstallEngineTrend
} from '@/api/machine/machinemechanical.js'
import { TooltipComponent } from '../../../components/jsx/TooltipComponent'
import { TooltipFormatter } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'

const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '装机数')?.value
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  quarter: '', // 季度
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  dataSource: dataSource, // 数据来源
  subMarket1: '', // 细分市场1
  manuFacturerTabs: '三一集团', // 主机厂
  manuFacturer: '三一集团', // 主机厂
  engineFactory: '', // 发动机厂
  breed: '', // 品系
  segment: '通机' // 板块
}
const data = reactive({
  params: { ...originParams },
  chartA: {
    title: '',
    data: [],
    legendData: [],
    loading: false
  },
  chartB: {
    title: '',
    data: [],
    legendData: [],
    loading: false
  },
  chartC: {
    title: '',
    data: [],
    loading: false
  },
  chartD: {
    title: '',
    data: [],
    loading: false
  }
})
const { yAxisRight, referData, setOneArraySeriesData, sortByArray, sortRankByFirstBarData } =
  calChartsData()

function getParams(params) {
  data.params = params
  initChartData()
}

/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = async () => {
  data.chartA.loading = true
  data.chartB.loading = true
  data.chartC.loading = true
  data.chartD.loading = true
  const monthSortArray = referData.monthSort
  const quarterSortArray = referData.quarterSort
  const params = JSON.parse(JSON.stringify(data.params))
  params.dataType = params.dataType ? params.dataType.join() : ''

  // 第一，二个图表
  hostCustomerInstallMarketTrend(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { marketLineList: lineB, marketTrendList: barB, marketYearList: barA } = resData
      // a.处理左边3年装机量
      barA.forEach(el => {
        el.xAxisName = el.year + '年'

        el.tooltipValue = el.sales ? `${numberFormat(el.sales, 0)}台` : ''
      })
      let seriesBarA = setOneArraySeriesData({
        list: barA,
        xAxisKey: 'xAxisName',
        yAxisKey: 'sales',
        legendKey: 'submarket2'
      })
      const currentYear = Number(params.year)
      const yearSort = []
      for (let i = 0; i < 3; i++) {
        yearSort.unshift(`${currentYear - i}年`)
      }

      seriesBarA = sortByArray(seriesBarA, yearSort, 'xAxisName')
      seriesBarA = sortRankByFirstBarData(seriesBarA, true, 'last')

      const legendAData = []
      seriesBarA.forEach(el => {
        if (
          el.name
          // &&
          // el.name.indexOf('工程机(其它)') === -1 &&
          // el.name.indexOf('农机(其它)') === -1 &&
          // el.name.indexOf('工程机（其它）') === -1 &&
          // el.name.indexOf('农机（其它）') === -1
        )
          legendAData.push(el)
      })
      seriesBarA.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartA.legendData = legendAData.reverse()
      data.chartA.data = seriesBarA
      data.chartA.title = `${params.manuFacturer}年度装机量趋势(${params.segment})`

      // b.右边最近⼀年⽉度、⽉累、季度装机量（柱⼦⾼度），需包含 其他，折线显示对应装机量总量同⽐，⿏标悬浮显示装机量、各实例装机量、各实 例装机量占⽐、各实例同⽐

      barB.forEach(el => {
        el.xAxisName = el.month + '月'
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        }
        el.tooltipValue = `装机量:${numberFormat(el.sales, 0) ?? 0}台;占比:${numberFormat(el.prop, 1) ?? 0}%;同比:${numberFormat(el.propChange_sc, 1) ?? 0}%`
      })
      let seriesBarB = setOneArraySeriesData({
        list: barB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'sales',
        legendKey: 'submarket2'
      })
      seriesBarB = sortByArray(
        seriesBarB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesBarB = sortRankByFirstBarData(seriesBarB)
      lineB.forEach(el => {
        el.propChange = el.propChange ? el.propChange : 0
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
        el.legendName = '同比'
        el.tooltipValue = `${el.propChange ?? 0}%`
      })
      let seriesLineB = setOneArraySeriesData({
        list: lineB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'propChange',
        legendKey: `legendName`
      })
      seriesLineB = sortByArray(
        seriesLineB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesLineB.forEach(el => {
        el.type = 'line'
        el.yAxisIndex = 1
        el.stack = null
      })
      seriesBarB.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      const seriesBData = [...seriesBarB, ...seriesLineB]
      const legendBData = []
      seriesBData.forEach(el => {
        if (
          el.name 
          // &&
          // el.name.indexOf('工程机(其它)') === -1 &&
          // el.name.indexOf('农机(其它)') === -1 &&
          // el.name.indexOf('工程机（其它）') === -1 &&
          // el.name.indexOf('农机（其它）') === -1
        )
          legendBData.push(el)
      })

      data.chartB.legendData = legendBData.reverse()

      data.chartB.data = seriesBData
      // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
      data.chartB.title = `${params.manuFacturer}${params.pointerType === '1' ? '季度' : params.pointerType === '2' ? '月累' : '月度'}装机量趋势(${params.segment})`
    })
    .finally(() => {
      data.chartA.loading = false
      data.chartB.loading = false
    })

  // 第三，四个图表
  hostCustomerInstallEngineTrend(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { engineLineList, engineTrendList: barB, engineYearList: barA } = resData

      // a.处理左边3年装机量
      barA.forEach(el => {
        el.xAxisName = el.year + '年'
        el.tooltipValue = el.sales ? `${numberFormat(el.sales, 0) ?? ''}台` : ''
      })
      let seriesBarA = setOneArraySeriesData({
        list: barA,
        xAxisKey: 'xAxisName',
        yAxisKey: 'sales',
        legendKey: 'engine'
      })
      const currentYear = Number(params.year)
      const yearSort = []
      for (let i = 0; i < 3; i++) {
        yearSort.unshift(`${currentYear - i}年`)
      }

      seriesBarA = sortByArray(seriesBarA, yearSort, 'xAxisName')
      seriesBarA = sortRankByFirstBarData(seriesBarA, true, 'last')
      seriesBarA.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      data.chartC.data = seriesBarA
      data.chartC.title = `${params.manuFacturer}发动机年度装机量趋势(${params.segment})`
      // b.右边右边最近⼀年装机量（柱⼦⾼度），折线优先显示⽟柴的占⽐
      barB.forEach(el => {
        el.xAxisName = el.month + '月'
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        }
        el.tooltipValue = `装机量:${numberFormat(el.sales, 0) ?? 0}台;占比:${numberFormat(el.prop) ?? 0}%;同比:${numberFormat(el.propChange_dl, 1) ?? 0}%`
      })
      let seriesBarB = setOneArraySeriesData({
        list: barB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'sales',
        legendKey: 'engine'
      })
      seriesBarB = sortByArray(
        seriesBarB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesBarB = sortRankByFirstBarData(seriesBarB)
      seriesBarB.forEach(el => {
        el.type = 'bar'
        el.stack = 'barStack'
      })
      let originList = []
      if (engineLineList && engineLineList[0] && engineLineList[0]['@type']) {
        delete engineLineList[0]['@type']
        originList = engineLineList[0]
      }
      let lineB = []
      let legendNameLineB = ''
      if (originList['玉柴']) {
        lineB = originList['玉柴']
        legendNameLineB = '玉柴占比'
      } else {
        for (let i in originList) {
          lineB = originList[i]
          legendNameLineB = i + '占比'
          break
        }
      }

      lineB.forEach(el => {
        el.propChange = el.propChange ? el.propChange : 0
        // pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
        if (params.pointerType === '1') {
          el.xAxisName = referData.quarterRefer[el.month]
        } else {
          el.month = Number(el.month)
          el.xAxisName = el.month + '月'
        }
        el.legendName = legendNameLineB
        el.tooltipValue = `${el.prop ?? 0}%`
      })
      let seriesLineB = setOneArraySeriesData({
        list: lineB,
        xAxisKey: 'xAxisName',
        yAxisKey: 'prop',
        legendKey: `legendName`
      })
      seriesLineB = sortByArray(
        seriesLineB,
        params.pointerType === '1' ? quarterSortArray : monthSortArray,
        'xAxisName'
      )
      seriesLineB.forEach(el => {
        el.type = 'line'
        el.yAxisIndex = 1
        el.stack = null
      })
      data.chartD.data = [...seriesBarB, ...seriesLineB]
      data.chartD.title = `${params.manuFacturer}发动机${params.pointerType === '1' ? '季度' : params.pointerType === '2' ? '月累' : '月度'}装机量趋势(${params.segment})`
    })
    .finally(() => {
      data.chartC.loading = false
      data.chartD.loading = false
    })
}

const TooltipSalesProportionYoYComponent = propos => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {item.seriesType == 'line'
                  ? `${item.seriesType == 'line' ? numberFormat(item.value, 1) : numberFormat(item.value, 0)}%`
                  : `${numberFormat(item.value, 0)||'0'}台 | ${numberFormat(item.data.proportion, 1)}% | ${numberFormat(item.data.yoy, 1)}%`}
              </span>
            </>
          )
        },
        'hander-right': () => {
          return (
            <>
              <span>销量｜占比｜同比</span>
            </>
          )
        },
        'total-num': ({ params }) => {

          // 计算总装机量，排除线型图表数据，确保数据安全性和准确性
          const totalZhuangji = params
            .filter(item => item?.seriesType !== 'line') // 先过滤掉线型图表数据
            .reduce((sum, item) => {
              // 检查数据项是否存在
              if (!item || !item.data) {
                return sum
              }

              // 获取销量数据并进行类型检查
              const sales = item.data.sales

              // 确保 sales 是有效的数字（包括 0）
              if (typeof sales === 'number' && !isNaN(sales) && isFinite(sales)) {
                return sum + sales
              }

              // 如果 sales 是字符串数字，尝试转换
              if (typeof sales === 'string' && sales.trim() !== '') {
                const parsedSales = parseFloat(sales)
                if (!isNaN(parsedSales) && isFinite(parsedSales)) {
                  return sum + parsedSales
                }
              }

              // 其他情况返回原始 sum（不加任何值）
              return sum
            }, 0)
          return (
            <>
              <span>{numberFormat(totalZhuangji,0)}台 </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>
<style lang="scss" scoped>
.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {
  margin-bottom: 0px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
